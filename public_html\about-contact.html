<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quikr Solutions - About & Contact</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="components/wave.css">
</head>
<body>
    <div id="navbar-container"></div>
    <script>
      fetch('components/navbar.html')
        .then(response => response.text())
        .then(data => {
          document.getElementById('navbar-container').innerHTML = data;
          // Initialize navigation after navbar is loaded
          if (window.initNavigation) {
            window.initNavigation();
          }
        });
    </script>
  <section class="about-hero bg-gradient-primary text-light">
    <div class="container hero-content p-5">
      <h1 class="mb-4">Innovating for the Caribbean</h1>
      <p class="mb-4">Discover our journey, values, and the team behind Quikr Solutions</p>
    </div>
    <!-- Wave Animation -->
    <div class="wave-container">
      <svg class="wave-animation" viewBox="0 0 1200 120" preserveAspectRatio="none">
        <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" class="shape-fill"></path>
        <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" class="shape-fill"></path>
        <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" class="shape-fill"></path>
      </svg>
    </div>
  </section>
  <section class="story-section section">
    <div class="container">
      <div class="story-container">
        <div class="story-image animate">
          <i class="fas fa-history" style="font-size: 5rem; color: var(--primary);"></i>
        </div>
        <div class="story-content animate">
          <h2 class="section-title">Our Story</h2>
          <p class="mb-3">Founded in 2023, Quikr Solutions emerged from a vision to transform the Caribbean digital landscape. We recognized the unique challenges and opportunities in our region and set out to create innovative solutions tailored specifically for Caribbean businesses and consumers.</p>
          <p class="mb-3">Starting from a small team in St. Kitts, we've grown into a regional leader in digital transformation, serving clients across 15 Caribbean nations with our cutting-edge platforms and services.</p>
          
          <div class="milestones">
            <div class="milestone-card animate">
              <div class="milestone-number">15+</div>
              <div>Caribbean Nations</div>
            </div>
            <div class="milestone-card animate">
              <div class="milestone-number">500+</div>
              <div>Business Clients</div>
            </div>
            <div class="milestone-card animate">
              <div class="milestone-number">10K+</div>
              <div>Active Users</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="team-section section">
    <div class="container">
      <h2 class="section-title text-center animate">Leadership Team</h2>
      <p class="section-subtitle text-center animate">Meet the innovative minds driving our success</p>
      
      <div class="team-grid">
        <div class="team-card animate">
          <div class="team-image">
            <img src="https://via.placeholder.com/120" alt="Robi J Robin">
          </div>
          <div class="team-info">
            <h4>Robi J Robin</h4>
            <span class="position">CEO & Founder</span>
            <p>Visionary leader with 15+ years in graphic design, web and mobile development, Computer Science and Caribbean market development.</p>
            <div class="team-social">
              <a href="#"><i class="fab fa-linkedin-in"></i></a>
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fas fa-envelope"></i></a>
            </div>
          </div>
        </div>
        
        <div class="team-card animate">
          <div class="team-image">
            <img src="https://via.placeholder.com/120" alt="Shakir Robin">
          </div>
          <div class="team-info">
            <h4>Shakir Robin</h4>
            <span class="position">CTO</span>
            <p>Tech expert specializing in AI systems and scalable architecture for island economies.</p>
            <div class="team-social">
              <a href="#"><i class="fab fa-linkedin-in"></i></a>
              <a href="#"><i class="fab fa-github"></i></a>
              <a href="#"><i class="fas fa-envelope"></i></a>
            </div>
          </div>
        </div>
        
        <div class="team-card animate">
          <div class="team-image">
            <img src="https://via.placeholder.com/120" alt="Dakar Robin">
          </div>
          <div class="team-info">
            <h4>Dakar Robin</h4>
            <span class="position">CFO</span>
            <p>Financial strategist with expertise in Caribbean fintech and sustainable growth.</p>
            <div class="team-social">
              <a href="#"><i class="fab fa-linkedin-in"></i></a>
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fas fa-envelope"></i></a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="values-section section">
    <div class="container">
      <h2 class="section-title text-center animate">Our Core Values</h2>
      <p class="section-subtitle text-center animate">Principles that guide everything we do</p>
      
      <div class="values-grid">
        <div class="value-card animate">
          <i class="fas fa-lightbulb value-icon"></i>
          <h3>Innovation</h3>
          <p>We constantly push boundaries to create cutting-edge solutions for Caribbean challenges.</p>
        </div>
        
        <div class="value-card animate">
          <i class="fas fa-hands-helping value-icon"></i>
          <h3>Community Focus</h3>
          <p>We build solutions that empower local businesses and strengthen Caribbean communities.</p>
        </div>
        
        <div class="value-card animate">
          <i class="fas fa-leaf value-icon"></i>
          <h3>Sustainability</h3>
          <p>We develop eco-conscious solutions that support long-term regional prosperity.</p>
        </div>
        
        <div class="value-card animate">
          <i class="fas fa-hand-holding-usd value-icon"></i>
          <h3>Economic Empowerment</h3>
          <p>We create tools that enable financial inclusion and growth opportunities for all.</p>
        </div>
      </div>
    </div>
  </section>

  <section id="contact" class="contact-section section">
    <div class="container">
      <h2 class="section-title text-center animate">Get In Touch</h2>
      <p class="section-subtitle text-center animate">We'd love to hear from you about partnerships or opportunities</p>
      
      <div class="contact-container">
        <div class="contact-info animate">
          <h3>Contact Information</h3>
          
          <div class="info-item">
            <div class="info-icon">
              <i class="fas fa-map-marker-alt info-icon"></i>
            </div>
            <div class="info-content">
              <h4>Headquarters</h4>
              <p>#3 Captain Street, Basseterre, St. Kitts</p>
            </div>
          </div>
          
          <div class="info-item">
            <div class="info-icon">
              <i class="fas fa-phone-alt info-icon"></i>
            </div>
            <div class="info-content">
              <h4>Phone Number</h4>
              <p><a href="tel:+18695671234">+****************</a></p>
            </div>
          </div>
          
          <div class="info-item">
            <div class="info-icon">
              <i class="fas fa-envelope info-icon"></i>
            </div>
            <div class="info-content">
              <h4>Email Address</h4>
              <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
              <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
          </div>
          
          <div class="info-item">
            <div class="info-icon">
              <i class="fas fa-clock info-icon"></i>
            </div>
            <div class="info-content">
              <h4>Business Hours</h4>
              <p>Monday - Friday: 8:00 AM - 5:00 PM</p>
              <p>Saturday: 9:00 AM - 1:00 PM</p>
            </div>
          </div>
          
          <div class="social-links">
            <a href="#" class="social-link">
              <i class="fab fa-facebook-f"></i></a>
            <a href="#" class="social-link">
              <i class="fab fa-twitter"></i></a>
            <a href="#" class="social-link">
              <i class="fab fa-linkedin-in"></i></a>
            <a href="#" class="social-link">
              <i class="fab fa-instagram"></i></a>
          </div>
        </div>
        
        <div class="contact-form animate">
          <h3 class="section-title" style="display: inline-block; margin-bottom: 2rem;">Send Us a Message</h3>
          
          <form id="contactForm">
            <div class="form-group">
              <label for="name" class="form-label">Full Name</label>
              <input type="text" id="name" class="form-control" placeholder="Enter your name" required>
            </div>
            
            <div class="form-group">
              <label for="email" class="form-label">Email Address</label>
              <input type="email" id="email" class="form-control" placeholder="Enter your email" required>
            </div>
            
            <div class="form-group">
              <label for="phone" class="form-label">Phone Number</label>
              <input type="tel" id="phone" class="form-control" placeholder="Enter your phone number">
            </div>
            
            <div class="form-group">
              <label for="subject" class="form-label">Subject</label>
              <select id="subject" class="form-control" required>
                <option value="" disabled selected>Select a subject</option>
                <option value="general">General Inquiry</option>
                <option value="support">Technical Support</option>
                <option value="partnership">Partnership Opportunities</option>
                <option value="feedback">Product Feedback</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div class="form-group">
              <label for="message" class="form-label">Your Message</label>
              <textarea id="message" class="form-control" placeholder="Type your message here" required></textarea>
            </div>
            
            <button type="submit" class="btn" style="width: 100%;">Send Message</button>
          </form>
        </div>
      </div>
    </div>
  </section>
  
  <section class="map-section">
    <div class="container animate">
      <div class="map-container">
        <div id="map"></div>
        <div class="map-overlay">
          <h3>Our Headquarters</h3>
          <p><i class="fas fa-map-marker-alt" style="color: var(--accent); margin-right: 8px;"></i> #3 Captain Street</p>
          <p><i class="fas fa-city" style="color: var(--accent); margin-right: 8px;"></i> Basseterre, St. Kitts</p>
          <p><i class="fas fa-clock" style="color: var(--accent); margin-right: 8px;"></i> Mon-Fri: 8AM-5PM</p>
        </div>
      </div>
    </div>
  </section>

  <footer style="background: var(--dark); color: white; padding: 4rem 0 2rem;">
    <div class="container">
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2.5rem; margin-bottom: 3rem;">
        <div>
          <div class="logo" style="margin-bottom: 1.5rem;">
            <div class="logo-placeholder">
              <span>Q</span>
            </div>
            <span class="logo-text" style="color: white;">Quikr Solutions</span>
          </div>
          <p style="margin-bottom: 1.5rem; opacity: 0.8;">Empowering businesses and individuals through innovative technology solutions.</p>
          <div style="display: flex; gap: 1rem;">
            <a href="#" style="width: 40px; height: 40px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-decoration: none; transition: var(--transition);">
              <i class="fab fa-facebook-f"></i>
            </a>
            <a href="#" style="width: 40px; height: 40px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-decoration: none; transition: var(--transition);">
              <i class="fab fa-twitter"></i>
            </a>
            <a href="#" style="width: 40px; height: 40px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-decoration: none; transition: var(--transition);">
              <i class="fab fa-linkedin-in"></i>
            </a>
            <a href="#" style="width: 40px; height: 40px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-decoration: none; transition: var(--transition);">
              <i class="fab fa-instagram"></i>
            </a>
          </div>
        </div>
        
        <div>
          <h3 style="font-size: 1.3rem; margin-bottom: 1.5rem; position: relative; padding-bottom: 0.5rem;">Quick Links</h3>
          <ul style="list-style: none;">
            <li style="margin-bottom: 0.8rem;"><a href="index.html" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Home</a></li>
            <li style="margin-bottom: 0.8rem;"><a href="about.html" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">About Us</a></li>
            <li style="margin-bottom: 0.8rem;"><a href="solutions.html" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Solutions</a></li>
            <li style="margin-bottom: 0.8rem;"><a href="market.html" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Caribbean Focus</a></li>
            <li><a href="contact.html" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Contact</a></li>
          </ul>
        </div>
        
        <div>
          <h3 style="font-size: 1.3rem; margin-bottom: 1.5rem; position: relative; padding-bottom: 0.5rem;">Legal</h3>
          <ul style="list-style: none;">
            <li style="margin-bottom: 0.8rem;"><a href="#" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Privacy Policy</a></li>
            <li style="margin-bottom: 0.8rem;"><a href="#" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Terms of Service</a></li>
            <li><a href="#" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Cookie Policy</a></li>
          </ul>
        </div>
        
        <div>
          <h3 style="font-size: 1.3rem; margin-bottom: 1.5rem; position: relative; padding-bottom: 0.5rem;">Contact</h3>
          <ul style="list-style: none;">
            <li style="margin-bottom: 1rem; display: flex; align-items: flex-start;">
              <i class="fas fa-envelope" style="margin-right: 1rem; color: var(--accent);"></i>
              <span><EMAIL></span>
            </li>
            <li style="margin-bottom: 1rem; display: flex; align-items: flex-start;">
              <i class="fas fa-map-marker-alt" style="margin-right: 1rem; color: var(--accent);"></i>
              <span>#3 Captain Street, Basseterre</span>
            </li>
            <li style="display: flex; align-items: flex-start;">
              <i class="fas fa-phone-alt" style="margin-right: 1rem; color: var(--accent);"></i>
              <span>+****************</span>
            </li>
          </ul>
        </div>
      </div>
      
      <div style="text-align: center; padding-top: 2rem; border-top: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.6); font-size: 0.9rem;">
        <p>&copy; 2023 Quikr Solutions. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
  <script>
    // Mobile Navigation Toggle
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');

    hamburger.addEventListener('click', () => {
      navMenu.classList.toggle('active');
      hamburger.innerHTML = navMenu.classList.contains('active') ? 
        '<i class="fas fa-times"></i>' : '<i class="fas fa-bars"></i>';
    });

    // Close menu when clicking links
    document.querySelectorAll('.nav-link').forEach(link => {
      link.addEventListener('click', () => {
        navMenu.classList.remove('active');
        hamburger.innerHTML = '<i class="fas fa-bars"></i>';
      });
    });

    // Animation on scroll
    document.addEventListener('DOMContentLoaded', function() {
      const animateElements = document.querySelectorAll('.animate');
      
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('visible');
          }
        });
      }, { threshold: 0.1 });

      animateElements.forEach(el => {
        observer.observe(el);
      });
    });

    // Initialize map
    function initMap() {
      // Coordinates for St. Kitts headquarters
      const map = L.map('map').setView([17.302, -62.717], 15);
      
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      }).addTo(map);
      
      const headquartersMarker = L.marker([17.302, -62.717]).addTo(map);
      headquartersMarker.bindPopup("<b>Quikr Solutions Headquarters</b><br>#3 Captain Street, Basseterre, St. Kitts").openPopup();
    }
    
    // Initialize map after page load
    document.addEventListener('DOMContentLoaded', initMap);
    
    // Form submission
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
      contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Form validation
        const name = document.getElementById('name').value;
        const email = document.getElementById('email').value;
        const subject = document.getElementById('subject').value;
        const message = document.getElementById('message').value;
        
        if (!name || !email || !subject || !message) {
          alert('Please fill in all required fields.');
          return;
        }
        
        // Here you would normally send the form data to your server
        // For this demo, we'll just show a success message
        alert('Thank you for your message! We will get back to you soon.');
        contactForm.reset();
      });
    }
  </script>

  <script type="module" src="js/main.js"></script>

  <div class="wave-container">
      <svg viewBox="0 0 1200 120" preserveAspectRatio="none">
          <path d="M0,56.5c0,0,298.6,0,400,0s400,0,400,0s298.6,0,400,0v63.1H0V56.5z" class="shape-fill"></path>
      </svg>
  </div>
</body>
</html>