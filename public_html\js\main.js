// Main JavaScript entry point
import { initNavigation } from './modules/navigation.js';
import { initAnimations } from './modules/animations.js';
import { initForms } from './modules/forms.js';
import { validateAndSubmitForm } from './modules/form-validation.js';

// Initialize all modules when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  initNavigation();
  initAnimations();
  initForms();

  // Make functions globally available
  window.validateAndSubmitForm = validateAndSubmitForm;
  window.initNavigation = initNavigation;
});
