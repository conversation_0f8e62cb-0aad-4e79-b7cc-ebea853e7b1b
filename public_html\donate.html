<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Support Quikr Solutions' initiatives through donations">
  <title>Quikr Solutions - Donate</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="components/wave.css">
</head>
<body>
  <div id="navbar-container"></div>
  <script>
    fetch('components/navbar.html')
      .then(response => response.text())
      .then(data => {
        document.getElementById('navbar-container').innerHTML = data;
        // Initialize navigation after navbar is loaded
        if (window.initNavigation) {
          window.initNavigation();
        }
      });
  </script>
  <style>
    /* ===== VARIABLES & RESET ===== */
    :root {
      --primary: #4361ee;
      --primary-dark: #3a0ca3;
      --secondary: #7209b7;
      --accent: #f72585;
      --green: #10b981;
      --green-dark: #059669;
      --light-blue: #4cc9f0;
      --light: #f8f9fa;
      --dark: #212529;
      --gray: #6c757d;
      --border: #dee2e6;
      --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
      --transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Poppins', sans-serif;
      line-height: 1.7;
      color: var(--dark);
      background-color: #fff;
      overflow-x: hidden;
    }

    h1, h2, h3, h4, h5 {
      font-family: 'Montserrat', sans-serif;
      font-weight: 700;
      line-height: 1.3;
    }

    .container {
      width: 90%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1.5rem;
    }

    .btn {
      display: inline-block;
      padding: 1rem 2.5rem;
      background: var(--green);
      color: white;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      transition: var(--transition);
      border: none;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      z-index: 1;
      box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
    }

    .btn:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: var(--green-dark);
      border-radius: 50px;
      z-index: -2;
    }

    .btn:before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0%;
      height: 100%;
      background: var(--primary);
      transition: all 0.3s;
      border-radius: 50px;
      z-index: -1;
    }

    .btn:hover {
      color: #fff;
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(16, 185, 129, 0.4);
    }

    .btn:hover:before {
      width: 100%;
    }

    .btn-outline {
      background: transparent;
      border: 2px solid var(--green);
      color: var(--green);
      box-shadow: none;
    }

    .btn-outline:before {
      background: var(--green);
    }

    .btn-outline:hover {
      color: white;
    }

    .section {
      padding: 6rem 0;
      position: relative;
    }

    .section-title {
      font-size: 2.8rem;
      margin-bottom: 1.5rem;
      position: relative;
      display: inline-block;
    }

    .section-title:after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 0;
      width: 70px;
      height: 4px;
      background: var(--accent);
      border-radius: 2px;
    }

    .section-subtitle {
      font-size: 1.25rem;
      color: var(--gray);
      max-width: 700px;
      margin: 0 auto 3rem;
    }

    .text-center {
      text-align: center;
    }

    /* ===== HEADER & NAVIGATION ===== */
    header {
      background: white;
      box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
      position: sticky;
      top: 0;
      z-index: 1000;
      padding: 0.5rem 0;
    }

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 0;
    }

    .logo {
      display: flex;
      align-items: center;
      text-decoration: none;
    }

    .logo-placeholder {
      width: 50px;
      height: 50px;
      background: linear-gradient(45deg, var(--green), var(--green-dark), var(--primary));
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      animation: gradientAnimation 8s ease infinite;
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    .logo-placeholder span {
      color: white;
      font-size: 2rem;
      font-weight: 800;
      font-family: 'Montserrat', sans-serif;
    }

    .logo-text {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--green-dark);
    }

    .nav-menu {
      display: flex;
      list-style: none;
      gap: 2.5rem;
    }

    .nav-link {
      text-decoration: none;
      color: var(--dark);
      font-weight: 500;
      transition: var(--transition);
      position: relative;
      padding: 0.5rem 0;
    }

    .nav-link:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0;
      height: 3px;
      background: var(--accent);
      transition: var(--transition);
      border-radius: 2px;
    }

    .nav-link:hover {
      color: var(--primary);
    }

    .nav-link:hover:after {
      width: 100%;
    }

    .nav-link.active {
      color: var(--primary);
    }

    .nav-link.active:after {
      width: 100%;
    }

    .hamburger {
      display: none;
      cursor: pointer;
      background: none;
      border: none;
      font-size: 1.8rem;
      color: var(--dark);
      z-index: 1000;
    }

    /* Dropdown Menu */
    .dropdown {
      position: relative;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      background-color: white;
      min-width: 200px;
      box-shadow: 0 8px 16px rgba(0,0,0,0.1);
      border-radius: 10px;
      padding: 0.5rem 0;
      z-index: 1;
      top: 100%;
      left: 0;
      margin-top: 0.5rem;
    }

    .dropdown:hover .dropdown-content {
      display: block;
    }

    .dropdown-item {
      display: block;
      padding: 0.75rem 1.5rem;
      text-decoration: none;
      color: var(--dark);
      transition: all 0.3s;
    }

    .dropdown-item:hover {
      background-color: rgba(16, 185, 129, 0.1);
      color: var(--green);
    }

    /* ===== DONATION HERO SECTION ===== */
    .donation-hero {
      background: linear-gradient(135deg, #0d9488 0%, #047857 100%);
      color: white;
      padding: 8rem 0 6rem;
      position: relative;
      overflow: hidden;
    }

    .donation-hero:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHBhdGggZD0iTTAgMGg0MHY0MEgweiIgZmlsbD0ibm9uZSIvPjxwYXRoIGQ9Ik0xMSAwdjQwTTAgMTFoNDBNMCAwaDQwdjQwSDB6IiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMC41IiBvcGFjaXR5PSIwLjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjcGF0dGVybikiLz48L3N2Zz4=');
      opacity: 0.15;
    }

    .hero-content {
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
      position: relative;
      z-index: 2;
    }

    .hero-content h1 {
      font-size: 3.8rem;
      line-height: 1.2;
      margin-bottom: 1.5rem;
      animation: fadeInUp 1s ease;
    }

    .hero-content p {
      font-size: 1.3rem;
      margin-bottom: 2.5rem;
      max-width: 700px;
      margin-left: auto;
      margin-right: auto;
      animation: fadeInUp 1s ease 0.2s forwards;
      opacity: 0;
    }

    /* ===== DONATION OPTIONS SECTION ===== */
    .donation-options {
      padding: 5rem 0;
    }

    .donation-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2.5rem;
      margin-top: 3rem;
    }

    .donation-card {
      padding: 2.5rem;
      border-radius: 20px;
      background: white;
      box-shadow: var(--card-shadow);
      transition: var(--transition);
      text-align: center;
      position: relative;
      overflow: hidden;
      border: 1px solid var(--border);
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .donation-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
    }

    .donation-icon {
      font-size: 3.5rem;
      color: var(--green);
      margin-bottom: 1.5rem;
      transition: var(--transition);
    }

    .donation-card:hover .donation-icon {
      transform: scale(1.1);
      color: var(--accent);
    }

    .donation-card h3 {
      font-size: 1.6rem;
      margin-bottom: 1rem;
      color: var(--green-dark);
    }

    .donation-card p {
      margin-bottom: 1.5rem;
      flex-grow: 1;
    }

    .qr-code {
      max-width: 200px;
      margin: 0 auto 1.5rem;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .qr-code img {
      width: 100%;
      height: auto;
      display: block;
    }

    .payment-partners {
      padding: 5rem 0;
      background-color: var(--light);
    }

    .partners-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
      align-items: center;
    }

    .partner-logo {
      text-align: center;
      padding: 1.5rem;
      background: white;
      border-radius: 15px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      transition: all 0.3s;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .partner-logo:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .partner-logo img {
      max-width: 100%;
      max-height: 80px;
    }

    .partner-placeholder {
      width: 100%;
      height: 80px;
      background: linear-gradient(45deg, #f3f4f6, #e5e7eb);
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--gray);
      font-weight: 600;
    }

    /* ===== IMPACT SECTION ===== */
    .impact-section {
      padding: 5rem 0;
      background: linear-gradient(to bottom, #f0f8ff, #e0f7fa);
    }

    .impact-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }

    .impact-card {
      text-align: center;
      padding: 2rem;
      background: white;
      border-radius: 15px;
      box-shadow: var(--card-shadow);
      transition: var(--transition);
    }

    .impact-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }

    .impact-number {
      font-size: 3rem;
      font-weight: 800;
      color: var(--green);
      margin-bottom: 0.5rem;
    }

    .impact-text {
      font-size: 1.1rem;
      color: var(--gray);
    }

    /* ===== ANIMATIONS ===== */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes float {
      0% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-15px);
      }
      100% {
        transform: translateY(0px);
      }
    }

    @keyframes gradientAnimation {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    .animate {
      opacity: 0;
      transform: translateY(30px);
      transition: opacity 0.8s ease, transform 0.8s ease;
    }

    .animate.visible {
      opacity: 1;
      transform: translateY(0);
    }

    .float {
      animation: float 4s ease-in-out infinite;
    }

    /* ===== RESPONSIVE DESIGN ===== */
    @media (max-width: 992px) {
      .hero-content h1 {
        font-size: 3rem;
      }
      
      .section-title {
        font-size: 2.4rem;
      }
    }

    @media (max-width: 768px) {
      .hamburger {
        display: block;
      }

      .nav-menu {
        position: fixed;
        top: 0;
        right: -100%;
        flex-direction: column;
        background: white;
        width: 280px;
        height: 100vh;
        text-align: left;
        transition: 0.4s;
        box-shadow: -5px 0 25px rgba(0, 0, 0, 0.08);
        padding: 6rem 2rem 2rem;
        gap: 1.5rem;
      }

      .nav-menu.active {
        right: 0;
      }
      
      .hero-content h1 {
        font-size: 2.5rem;
      }

      .dropdown-content {
        position: static;
        box-shadow: none;
        margin-top: 0.5rem;
        margin-bottom: 0.5rem;
        display: none;
      }

      .dropdown.active .dropdown-content {
        display: block;
      }
    }

    @media (max-width: 576px) {
      .hero-content h1 {
        font-size: 2.2rem;
      }
      
      .donation-grid,
      .partners-grid,
      .impact-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
    <header>
        <div class="container nav-container">
            <a href="#" class="logo">
                <div class="logo-placeholder">
                    <span>QS</span>
                </div>
                <span class="logo-text">Quikr Solutions</span>
            </a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="#" class="nav-link active">Home</a></li>
                    <li><a href="#" class="nav-link">Solutions</a></li>
                    <li class="dropdown">
                        <span class="dropdown-trigger">
                            Company
                            <i class="fas fa-chevron-down"></i>
                        </span>
                        <div class="dropdown-content">
                            <a href="#" class="dropdown-item">About Us</a>
                            <a href="#" class="dropdown-item">Contact</a>
                            <a href="#" class="dropdown-item">Sustainability</a>
                            <a href="#" class="dropdown-item">Donate</a>
                        </div>
                    </li>
                    <li><a href="#" class="nav-link">Pricing</a></li>
                    <li><a href="#" class="nav-link">Graphic Trends</a></li>
                    <li><a href="#" class="nav-link">Documentation</a></li>
                </ul>
            </nav>
            <button type="button" class="hamburger" aria-label="Menu toggle">
                <i class="fa-solid fa-bars"></i>
            </button>
        </div>
    </header>

    <!-- Header -->
    <header>
        <div class="container nav-container">
            <a href="index.html" class="logo">Qkr</a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="solutions/index.html" class="nav-link">Solutions</a></li>
                    <li><a href="sustainability.html" class="nav-link">Sustainability</a></li>
                    <li><a href="donate.html" class="nav-link">Donate</a></li>
                    <li><a href="pricing.html" class="nav-link">Pricing</a></li>
                    <li><a href="graphic-trends.html" class="nav-link">Graphic Trends</a></li>
                    <li><a href="about-contact.html" class="nav-link">About &amp; Contact</a></li>
                </ul>
            </nav>
            <button type="button" class="hamburger" aria-label="Menu toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </button>
        </div>
    </header>

  <main>
    <!-- Hero Section -->
    <section class="donation-hero">
      <div class="container">
        <div class="hero-content">
          <h1>Support Our Vision</h1>
          <p>Help us create sustainable solutions for the Caribbean and beyond. Your contribution makes a difference in building a greener, more connected future.</p>
          <a href="#donate-now" class="btn">Donate Now</a>
        </div>
      </div>
    </section>

    <!-- Donation Options Section -->
    <section id="donate-now" class="donation-options section">
      <div class="container">
        <h2 class="section-title text-center">Ways to Contribute</h2>
        <p class="section-subtitle text-center">Choose your preferred method to support our initiatives</p>
        
        <div class="donation-grid">
          <!-- PayPal Option -->
          <div class="donation-card">
            <i class="fab fa-paypal donation-icon"></i>
            <h3>PayPal</h3>
            <p>Make a secure donation through PayPal, one of the world's most trusted payment platforms.</p>
            <a href="https://www.paypal.com/ncp/payment/U9PXF9TLUBJHQ" target="_blank" class="btn">Donate via PayPal</a>
          </div>
          
          <!-- QR Code Option -->
          <div class="donation-card">
            <i class="fas fa-qrcode donation-icon"></i>
            <h3>Quikr+ Seed Startup</h3>
            <p>Scan this QR code to make a direct contribution to our seed startup initiative.</p>
            <div class="qr-code">
              <img src="images/qr-codes/crypto-donation.png" alt="Quikr+ Seed Startup QR Code">
            </div>
            <p>Support our growth and innovation</p>
          </div>
          
          <!-- Crypto Option (Future) -->
          <div class="donation-card">
            <i class="fas fa-coins donation-icon"></i>
            <h3>Cryptocurrency</h3>
            <p>Coming soon! We're working on integrating cryptocurrency donations for those who prefer this method.</p>
            <button class="btn btn-outline" disabled>Coming Soon</button>
          </div>
        </div>
      </div>
    </section>

    <!-- Payment Partners Section -->
    <section class="payment-partners section">
      <div class="container">
        <h2 class="section-title text-center">Our Payment Partners</h2>
        <p class="section-subtitle text-center">We work with trusted payment processors to ensure secure transactions</p>
        
        <div class="partners-grid">
          <!-- PayPal -->
          <div class="partner-logo">
            <i class="fab fa-paypal" style="font-size: 3rem; color: #00457C;"></i>
          </div>
          
          <!-- Eloh Processing -->
          <div class="partner-logo">
            <div class="partner-placeholder">Eloh Processing</div>
          </div>
          
          <!-- DCash -->
          <div class="partner-logo">
            <div class="partner-placeholder">DCash</div>
          </div>
          
          <!-- Tron Network -->
          <div class="partner-logo">
            <div class="partner-placeholder">Tron Network</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Impact Section -->
    <section class="impact-section section">
      <div class="container">
        <h2 class="section-title text-center">Your Impact</h2>
        <p class="section-subtitle text-center">See how your contributions are making a difference</p>
        
        <div class="impact-grid">
          <div class="impact-card">
            <div class="impact-number">85%</div>
            <div class="impact-text">of 2025 Carbon Credits Goal Achieved</div>
          </div>
          
          <div class="impact-card">
            <div class="impact-number">72%</div>
            <div class="impact-text">of 2025 e-Waste Initiative Goal Achieved</div>
          </div>
          
          <div class="impact-card">
            <div class="impact-number">100%</div>
            <div class="impact-text">Renewable Energy Operations Goal Achieved</div>
          </div>
          
          <div class="impact-card">
            <div class="impact-number">50+</div>
            <div class="impact-text">Local Businesses Supported</div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer style="padding: 3rem 0; background-color: #212529; color: white; text-align: center;">
    <div class="container">
      <div style="display: flex; justify-content: space-between; flex-wrap: wrap; margin-bottom: 2rem;">
        <div style="flex: 1; min-width: 250px; margin-bottom: 1.5rem;">
          <h3 style="margin-bottom: 1rem; color: white;">Quikr Solutions</h3>
          <p>Building sustainable solutions for the Caribbean and beyond.</p>
        </div>
        <div style="flex: 1; min-width: 250px; margin-bottom: 1.5rem;">
          <h3 style="margin-bottom: 1rem; color: white;">Quick Links</h3>
          <ul style="list-style: none;">
            <li><a href="index.html" style="color: white; text-decoration: none;">Home</a></li>
            <li><a href="#" style="color: white; text-decoration: none;">About</a></li>
            <li><a href="#" style="color: white; text-decoration: none;">Solutions</a></li>
            <li><a href="#" style="color: white; text-decoration: none;">Contact</a></li>
          </ul>
        </div>
        <div style="flex: 1; min-width: 250px; margin-bottom: 1.5rem;">
          <h3 style="margin-bottom: 1rem; color: white;">Connect With Us</h3>
          <div style="display: flex; gap: 1rem; justify-content: center;">
            <a href="#" style="color: white; font-size: 1.5rem;"><i class="fab fa-facebook"></i></a>
            <a href="#" style="color: white; font-size: 1.5rem;"><i class="fab fa-twitter"></i></a>
            <a href="#" style="color: white; font-size: 1.5rem;"><i class="fab fa-instagram"></i></a>
            <a href="#" style="color: white; font-size: 1.5rem;"><i class="fab fa-linkedin"></i></a>
          </div>
        </div>
      </div>
      <hr style="border-color: rgba(255,255,255,0.1); margin-bottom: 1.5rem;">
      <p>&copy; 2025 Quikr Solutions. All rights reserved.</p>
    </div>
  </footer>

  <script>
    // Mobile Menu Toggle
    document.addEventListener('DOMContentLoaded', function() {
      const hamburger = document.querySelector('.hamburger');
      const navMenu = document.querySelector('.nav-menu');
      
      hamburger.addEventListener('click', function() {
        navMenu.classList.toggle('active');
      });
      
      // Mobile dropdown toggle
      const dropdowns = document.querySelectorAll('.dropdown');
      
      dropdowns.forEach(dropdown => {
        dropdown.addEventListener('click', function(e) {
          if (window.innerWidth <= 768) {
            e.preventDefault();
            this.classList.toggle('active');
          }
        });
      });
      
      // Animation on scroll
      const animateElements = document.querySelectorAll('.animate');
      
      function checkIfInView() {
        animateElements.forEach(element => {
          const elementTop = element.getBoundingClientRect().top;
          const elementVisible = 150;
          
          if (elementTop < window.innerHeight - elementVisible) {
            element.classList.add('visible');
          }
        });
      }
      
      window.addEventListener('scroll', checkIfInView);
      checkIfInView();
    });
  </script>

  
    <div class="wave-container">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none">
            <path d="M0,56.5c0,0,298.6,0,400,0s400,0,400,0s298.6,0,400,0v63.1H0V56.5z" class="shape-fill"></path>
        </svg>
    </div>
</body>
</html>
