<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Driver Registration - Quikr Solutions</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../components/wave.css">
</head>
<body>
    <div id="navbar-container"></div>
    <script>
      fetch('../components/navbar.html')
        .then(response => response.text())
        .then(data => {
          document.getElementById('navbar-container').innerHTML = data;
          // Initialize navigation after navbar is loaded
          if (window.initNavigation) {
            window.initNavigation();
          }
        });
    </script>

    <!-- Hero Section -->
    <section class="hero bg-gradient-secondary">
        <div class="container">
            <div class="hero-content">
                <h1 class="animate">Driver Registration</h1>
                <p class="animate delay-1">Join our network of professional drivers for taxi, pickup trucks, vans, and heavy equipment transport.</p>
            </div>
        </div>
        <!-- Wave Animation -->
        <div class="wave-container">
            <svg class="wave-animation" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" class="shape-fill"></path>
            </svg>
        </div>
    </section>

    <!-- Vehicle Categories Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">Vehicle Categories</h2>
            <p class="section-subtitle">Choose the category that best fits your vehicle and driving experience.</p>

            <div class="features-grid">
                <div class="feature-card animate">
                    <i class="fas fa-taxi feature-icon"></i>
                    <h3>Taxi Services</h3>
                    <p>Provide passenger transportation services with your licensed taxi vehicle.</p>
                    <ul class="feature-list">
                        <li>Airport transfers</li>
                        <li>City rides</li>
                        <li>Scheduled pickups</li>
                    </ul>
                </div>

                <div class="feature-card animate">
                    <i class="fas fa-truck-pickup feature-icon"></i>
                    <h3>Pickup Trucks</h3>
                    <p>Transport goods and materials with your pickup truck for local deliveries.</p>
                    <ul class="feature-list">
                        <li>Furniture delivery</li>
                        <li>Construction materials</li>
                        <li>Moving services</li>
                    </ul>
                </div>

                <div class="feature-card animate">
                    <i class="fas fa-truck feature-icon"></i>
                    <h3>Vans & Trucks</h3>
                    <p>Handle larger cargo and commercial deliveries with vans and medium trucks.</p>
                    <ul class="feature-list">
                        <li>Commercial deliveries</li>
                        <li>Bulk transport</li>
                        <li>Event logistics</li>
                    </ul>
                </div>

                <div class="feature-card animate">
                    <i class="fas fa-truck-monster feature-icon"></i>
                    <h3>Heavy Equipment</h3>
                    <p>Specialized transport for heavy machinery and oversized cargo.</p>
                    <ul class="feature-list">
                        <li>Construction equipment</li>
                        <li>Industrial machinery</li>
                        <li>Specialized cargo</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="section bg-light">
        <div class="container">
            <h2 class="section-title">Driver Benefits</h2>
            <div class="two-column">
                <div>
                    <div class="feature-item">
                        <i class="fas fa-dollar-sign"></i>
                        <div>
                            <h4>Competitive Earnings</h4>
                            <p>Earn competitive rates with transparent pricing and no hidden fees.</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-calendar-alt"></i>
                        <div>
                            <h4>Flexible Schedule</h4>
                            <p>Work when you want with complete control over your schedule.</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-mobile-alt"></i>
                        <div>
                            <h4>Easy-to-Use App</h4>
                            <p>Manage bookings, track earnings, and communicate with customers.</p>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="feature-item">
                        <i class="fas fa-shield-alt"></i>
                        <div>
                            <h4>Insurance Coverage</h4>
                            <p>Comprehensive insurance coverage while you're on the job.</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-headset"></i>
                        <div>
                            <h4>24/7 Support</h4>
                            <p>Round-the-clock support team to help with any issues.</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-credit-card"></i>
                        <div>
                            <h4>Fast Payments</h4>
                            <p>Weekly payments directly to your bank account.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Registration Form Section -->
    <section class="section">
        <div class="container">
            <div class="two-column">
                <div>
                    <h2 class="section-title">Join Our Driver Network</h2>
                    <p class="section-subtitle">Complete the registration form to start your journey as a Quikr driver.</p>
                    
                    <div class="requirements-box">
                        <h4>Requirements:</h4>
                        <ul>
                            <li>Valid driver's license (minimum 2 years)</li>
                            <li>Vehicle registration and insurance</li>
                            <li>Clean driving record</li>
                            <li>Background check clearance</li>
                            <li>Vehicle inspection certificate</li>
                        </ul>
                    </div>
                </div>

                <div class="contact-form">
                    <h3>Driver Registration Form</h3>
                    <form id="driver-registration-form">
                        <div class="form-group">
                            <label class="form-label">Full Name *</label>
                            <input type="text" class="form-control" name="full_name" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Email Address *</label>
                            <input type="email" class="form-control" name="email" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Phone Number *</label>
                            <input type="tel" class="form-control" name="phone" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Date of Birth *</label>
                            <input type="date" class="form-control" name="date_of_birth" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Address *</label>
                            <textarea class="form-control" name="address" rows="3" required></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Driver's License Number *</label>
                            <input type="text" class="form-control" name="license_number" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Years of Driving Experience *</label>
                            <select class="form-control" name="driving_experience" required>
                                <option value="">Select Experience</option>
                                <option value="2-5">2-5 years</option>
                                <option value="5-10">5-10 years</option>
                                <option value="10-15">10-15 years</option>
                                <option value="15+">15+ years</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Vehicle Category *</label>
                            <select class="form-control" name="vehicle_category" required>
                                <option value="">Select Category</option>
                                <option value="taxi">Taxi</option>
                                <option value="pickup">Pickup Truck</option>
                                <option value="van">Van</option>
                                <option value="truck">Medium Truck</option>
                                <option value="heavy">Heavy Equipment Transport</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Vehicle Make & Model *</label>
                            <input type="text" class="form-control" name="vehicle_model" placeholder="e.g., Toyota Camry 2020" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Vehicle Registration Number *</label>
                            <input type="text" class="form-control" name="registration_number" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Insurance Policy Number *</label>
                            <input type="text" class="form-control" name="insurance_policy" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Preferred Working Hours</label>
                            <select class="form-control" name="working_hours">
                                <option value="">Select Preference</option>
                                <option value="morning">Morning (6 AM - 12 PM)</option>
                                <option value="afternoon">Afternoon (12 PM - 6 PM)</option>
                                <option value="evening">Evening (6 PM - 12 AM)</option>
                                <option value="night">Night (12 AM - 6 AM)</option>
                                <option value="flexible">Flexible</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Additional Information</label>
                            <textarea class="form-control" name="additional_info" rows="3" placeholder="Any additional information about your experience or special qualifications..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-container">
                                <input type="checkbox" name="terms_accepted" required>
                                <span class="checkmark"></span>
                                I agree to the <a href="#" target="_blank">Terms and Conditions</a> and <a href="#" target="_blank">Privacy Policy</a>
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">Submit Application</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>Quikr Solutions</h3>
                    <p>Building sustainable solutions for the Caribbean and beyond.</p>
                </div>
                <div class="footer-column">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="../about-contact.html">About</a></li>
                        <li><a href="index.html">Solutions</a></li>
                        <li><a href="../about-contact.html#contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Connect With Us</h3>
                    <div class="social-icons">
                        <a href="#" aria-label="Facebook">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C6.477 2 2 6.477 2 12C2 17.523 6.477 22 12 22C17.523 22 22 17.523 22 12C22 6.477 17.523 2 12 2Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M15 8H13C12.4696 8 11.9609 8.21071 11.5858 8.58579C11.2107 8.96086 11 9.46957 11 10V22" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 13H16" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="Twitter">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 4.01C21 4.5 20.02 4.69 19 5C17.879 3.735 16.217 3.665 14.62 4.263C13.023 4.861 11.977 6.323 12 8.01V9.01C8.755 9.083 5.865 7.605 4 5.01C4 5.01 -0.182 12.433 8 16.01C6.128 17.247 4.261 18.088 2 18.01C5.308 19.687 8.913 20.322 12.034 19.503C15.614 18.565 18.556 15.935 19.685 11.882C20.0218 10.4988 20.1789 9.07701 20.152 7.653C20.152 7.493 21.692 5.513 22 4.009V4.01Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="Instagram">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <rect x="2" y="2" width="20" height="20" rx="5" ry="5" stroke="white" stroke-width="1.5"/>
                                <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37Z" stroke="white" stroke-width="1.5"/>
                                <line x1="17.5" y1="6.5" x2="17.51" y2="6.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Quikr Solutions. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script type="module" src="../js/main.js"></script>
</body>
</html>
