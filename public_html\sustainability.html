<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Quikr Solutions' commitment to environmental sustainability and green technology innovation">
  <title>Quikr Solutions - Sustainability & Innovation</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="components/wave.css">
</head>
<body>
  <div id="navbar-container"></div>
  <script>
    fetch('components/navbar.html')
      .then(response => response.text())
      .then(data => {
        document.getElementById('navbar-container').innerHTML = data;
        // Initialize navigation after navbar is loaded
        if (window.initNavigation) {
          window.initNavigation();
        }
      });
  </script>
  <style>
    /* ===== VARIABLES & RESET ===== */
    :root {
      --primary: #4361ee;
      --primary-dark: #3a0ca3;
      --secondary: #7209b7;
      --accent: #f72585;
      --green: #10b981;
      --green-dark: #059669;
      --light-blue: #4cc9f0;
      --light: #f8f9fa;
      --dark: #212529;
      --gray: #6c757d;
      --border: #dee2e6;
      --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
      --transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Poppins', sans-serif;
      line-height: 1.7;
      color: var(--dark);
      background-color: #fff;
      overflow-x: hidden;
    }

    h1, h2, h3, h4, h5 {
      font-family: 'Montserrat', sans-serif;
      font-weight: 700;
      line-height: 1.3;
    }

    .container {
      width: 90%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1.5rem;
    }

    .btn {
      display: inline-block;
      padding: 1rem 2.5rem;
      background: var(--green);
      color: white;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      transition: var(--transition);
      border: none;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      z-index: 1;
      box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
    }

    .btn:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: var(--green-dark);
      border-radius: 50px;
      z-index: -2;
    }

    .btn:before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0%;
      height: 100%;
      background: var(--primary);
      transition: all 0.3s;
      border-radius: 50px;
      z-index: -1;
    }

    .btn:hover {
      color: #fff;
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(16, 185, 129, 0.4);
    }

    .btn:hover:before {
      width: 100%;
    }

    .btn-outline {
      background: transparent;
      border: 2px solid var(--green);
      color: var(--green);
      box-shadow: none;
    }

    .btn-outline:before {
      background: var(--green);
    }

    .btn-outline:hover {
      color: white;
    }

    .section {
      padding: 6rem 0;
      position: relative;
    }

    .section-title {
      font-size: 2.8rem;
      margin-bottom: 1.5rem;
      position: relative;
      display: inline-block;
    }

    .section-title:after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 0;
      width: 70px;
      height: 4px;
      background: var(--accent);
      border-radius: 2px;
    }

    .section-subtitle {
      font-size: 1.25rem;
      color: var(--gray);
      max-width: 700px;
      margin: 0 auto 3rem;
    }

    .text-center {
      text-align: center;
    }

    /* ===== WAVE ANIMATION ===== */
    .wave-container {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 190%;
      overflow: hidden;
      line-height: 0;
      transform: rotate(180deg);
      z-index: 1;
    }

    .wave-container svg {
      position: relative;
      display: fill;
      width: calc(100% + 1.3px);
      height: 80px;
    }

    .wave-container .shape-fill {
      fill: #FFFFFF;
    }

    .wave-animation {
      animation: wave 85s cubic-bezier(0.36, 0.45, 0.63, 0.53) infinite;
      transform: translate3d(60, 0, 3);
    }

    @keyframes wave {
      0% {
        transform: translateX(25%);
      }
      -120% {
        transform: translateX(40%);
      }
    }

    .wave-animation path {
      animation: wave 25s cubic-bezier(0.36, 0.45, 0.63, 0.53) infinite;
    }

    .wave-animation path:nth-child(1) {
      animation-delay: -10s;
      animation-duration: 15s;
    }

    .wave-animation path:nth-child(2) {
      animation-delay: -23s;
      animation-duration: 10s;
    }

    .wave-animation path:nth-child(3) {
      animation-delay: -16s;
      animation-duration: 7.5s;
    }

    /* ===== HEADER & NAVIGATION ===== */
    header {
      background: white;
      box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
      position: sticky;
      top: 0;
      z-index: 1000;
      padding: 0.5rem 0;
    }

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 0;
    }

    .logo {
      display: flex;
      align-items: center;
      text-decoration: none;
    }

    .logo-placeholder {
      width: 50px;
      height: 50px;
      background: linear-gradient(45deg, var(--green), var(--green-dark), var(--primary));
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      animation: gradientAnimation 8s ease infinite;
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    .logo-placeholder span {
      color: white;
      font-size: 2rem;
      font-weight: 800;
      font-family: 'Montserrat', sans-serif;
    }

    .logo-text {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--green-dark);
    }

    .nav-menu {
      display: flex;
      list-style: none;
      gap: 2.5rem;
    }

    .nav-link {
      text-decoration: none;
      color: var(--dark);
      font-weight: 500;
      transition: var(--transition);
      position: relative;
      padding: 0.5rem 0;
    }

    .nav-link:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0;
      height: 3px;
      background: var(--accent);
      transition: var(--transition);
      border-radius: 2px;
    }

    .nav-link:hover {
      color: var(--primary);
    }

    .nav-link:hover:after {
      width: 100%;
    }

    .nav-link.active {
      color: var(--primary);
    }

    .nav-link.active:after {
      width: 100%;
    }

    .hamburger {
      display: none;
      cursor: pointer;
      background: none;
      border: none;
      font-size: 1.8rem;
      color: var(--dark);
      z-index: 1000;
    }

    /* ===== Sustainability HERO SECTION ===== */
    .solutions-hero {
      background: linear-gradient(135deg, #0d9488 0%, #047857 100%);
      color: white;
      padding: 8rem 0 6rem;
      position: relative;
      overflow: hidden;
    }

    .solutions-hero:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHBhdGggZD0iTTAgMGg0MHY0MEgweiIgZmlsbD0ibm9uZSIvPjxwYXRoIGQ9Ik0xMSAwdjQwTTAgMTFoNDBNMCAwaDQwdjQwSDB6IiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMC41IiBvcGFjaXR5PSIwLjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjcGF0dGVybikiLz48L3N2Zz4=');
      opacity: 0.15;
    }

    .hero-content {
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
      position: relative;
      z-index: 2;
    }

    .hero-content h1 {
      font-size: 3.8rem;
      line-height: 1.2;
      margin-bottom: 1.5rem;
      animation: fadeInUp 1s ease;
    }

    .hero-content p {
      font-size: 1.3rem;
      margin-bottom: 2.5rem;
      max-width: 700px;
      margin-left: auto;
      margin-right: auto;
      animation: fadeInUp 1s ease 0.2s forwards;
      opacity: 0;
    }

    /* ===== SOLUTIONS GRID ===== */
    .solutions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2.5rem;
      margin-top: 3rem;
    }

    .solution-card {
      padding: 2.5rem;
      border-radius: 20px;
      background: white;
      box-shadow: var(--card-shadow);
      transition: var(--transition);
      text-align: center;
      position: relative;
      overflow: hidden;
      border: 1px solid var(--border);
      display: flex;
      flex-direction: column;
    }

    .solution-card:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 5px;
      height: 0;
      background: var(--green);
      transition: var(--transition);
    }

    .solution-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
    }

    .solution-card:hover:before {
      height: 100%;
    }

    .solution-icon {
      font-size: 3.5rem;
      color: var(--green);
      margin-bottom: 1.5rem;
      transition: var(--transition);
    }

    .solution-card:hover .solution-icon {
      transform: scale(1.1);
      color: var(--accent);
    }

    .solution-card h3 {
      font-size: 1.6rem;
      margin-bottom: 1rem;
      color: var(--green-dark);
    }

    /* ===== Innovation SECTION ===== */
    .innovation-section {
      background: linear-gradient(to bottom, #ffffff, #e0f7fa);
      position: relative;
      overflow: hidden;
    }

    .innovation-section:before {
      content: '';
      position: absolute;
      top: 60px;
      right: -140px;
      width: 300px;
      height: 300px;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, rgba(247, 37, 133, 0.1) 100%);
      z-index: 0;
    }

    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }

    .feature-card {
      background: white;
      border-radius: 20px;
      padding: 2rem;
      box-shadow: var(--card-shadow);
      transition: var(--transition);
      position: relative;
      overflow: hidden;
      z-index: 1;
    }

    .feature-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .feature-icon {
      font-size: 2.5rem;
      color: var(--green);
      margin-bottom: 1.5rem;
    }

    .feature-card h4 {
      font-size: 1.4rem;
      margin-bottom: 1rem;
      color: var(--green-dark);
    }

    /* ===== Environmental Commitment SECTION ===== */
    .commitment-section {
      background: linear-gradient(to bottom, #fff0f8, #f8f0ff);
      position: relative;
    }

    .stories-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }

    .story-card {
      background: white;
      border-radius: 20px;
      padding: 2rem;
      box-shadow: var(--card-shadow);
      transition: var(--transition);
      text-align: center;
      position: relative;
      overflow: hidden;
      z-index: 1;
    }

    .story-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }

    /* ===== ANIMATIONS ===== */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* ===== UTILITIES ===== */
    .bg-gradient-primary {
      background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    }

    .bg-gradient-secondary {
      background: linear-gradient(135deg, var(--secondary) 0%, var(--accent) 100%);
    }

    .hover-lift {
      transition: var(--transition);
    }

    .hover-lift:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .w-100 {
      width: 100%;
    }

    .mb-2 {
      margin-bottom: 0.5rem;
    }

    .mb-3 {
      margin-bottom: 1rem;
    }

    .mb-4 {
      margin-bottom: 1.5rem;
    }

    .mt-5 {
      margin-top: 3rem;
    }

    .text-accent {
      color: var(--accent);
    }

    .text-primary {
      color: var(--primary);
    }

    .text-secondary {
      color: var(--secondary);
    }

    .card-content-wrapper {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
    }
  </style>
</head>
<body>
    <header>
        <div class="container nav-container">
            <a href="#" class="logo">
                <div class="logo-placeholder">
                    <span>QS</span>
                </div>
                <span class="logo-text">Quikr Solutions</span>
            </a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="solutions/index.html" class="nav-link">Solutions</a></li>
                    <li class="dropdown">
                        <span class="dropdown-trigger">
                            Company
                            <i class="fas fa-chevron-down"></i>
                        </span>
                        <div class="dropdown-content">
                            <a href="about-contact.html" class="dropdown-item">About Us</a>
                            <a href="about-contact.html#contact" class="dropdown-item">Contact</a>
                            <a href="sustainability.html" class="dropdown-item">Sustainability</a>
                            <a href="donate.html" class="dropdown-item">Donate</a>
                        </div>
                    </li>
                    <li><a href="pricing.html" class="nav-link">Pricing</a></li>
                    <li><a href="graphic-trends.html" class="nav-link">Graphic Trends</a></li>
                    <li><a href="documentation.md" class="nav-link">Documentation</a></li>
                </ul>
            </nav>
            <button type="button" class="hamburger" aria-label="Menu toggle">
                <i class="fa-solid fa-bars"></i>
            </button>
        </div>
    </header>

  <section class="solutions-hero">
    <div class="container hero-content">
      <h1 class="animate">Sustainability & Innovation</h1>
      <p class="animate delay-1">Driving Caribbean prosperity through green technology and sustainable practices.</p>
      <div style="display: flex; justify-content: center; gap: 1.5rem; margin-top: 2rem;">
        <a href="#innovation" class="btn btn-outline btn-animate">Explore Innovation</a>
        <a href="#commitment" class="btn btn-primary btn-animate">Our Commitment</a>
      </div>
    </div>
    <div class="wave-container">
      <svg class="wave-animation" viewBox="0 0 1200 120" preserveAspectRatio="none">
        <path d="M0,56.5c0,0,298.6,0,400,0s400,0,400,0s298.6,0,400,0v63.1H0V56.5z" class="shape-fill"></path>
      </svg>
    </div>
  </section>

  <section id="innovation" class="section innovation-section">
    <div class="container">
      <h2 class="section-title text-center animate">Green Technology Innovation</h2>
      <p class="section-subtitle text-center animate">Pioneering sustainable solutions for a greener Caribbean future.</p>
      <div class="grid-cols-2">
        <div class="animate">
          <h3 class="mb-3">Sustainable Solutions</h3>
          <ul class="feature-list">
            <li>
              <i class="fas fa-solar-panel"></i>
              <div>
                <h4>Renewable Energy Systems</h4>
                <p>Solar, wind, and hydro solutions tailored for island environments.</p>
              </div>
            </li>
            <li>
              <i class="fas fa-recycle"></i>
              <div>
                <h4>Waste Management Technologies</h4>
                <p>Innovative recycling and waste-to-energy systems.</p>
              </div>
            </li>
            <li>
              <i class="fas fa-tint"></i>
              <div>
                <h4>Water Purification</h4>
                <p>Advanced water treatment and conservation technologies.</p>
              </div>
            </li>
          </ul>
        </div>
        <div class="animate">
          <h3 class="mb-3">Smart Agriculture</h3>
          <ul class="feature-list">
            <li>
              <i class="fas fa-seedling"></i>
              <div>
                <h4>Precision Farming</h4>
                <p>Data-driven agriculture for efficient resource use.</p>
              </div>
            </li>
            <li>
              <i class="fas fa-tree"></i>
              <div>
                <h4>Sustainable Forestry</h4>
                <p>Eco-friendly forestry practices and reforestation projects.</p>
              </div>
            </li>
            <li>
              <i class="fas fa-fish"></i>
              <div>
                <h4>Aquaculture Innovation</h4>
                <p>Sustainable aquaculture and fisheries management.</p>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <section class="section commitment-section">
    <div class="container">
      <h2 class="section-title text-center animate">Environmental Commitment</h2>
      <p class="section-subtitle text-center animate">Our dedication to preserving the Caribbean's natural beauty.</p>
      <div class="solutions-grid">
        <div class="solution-card animate">
          <i class="fas fa-globe-americas solution-icon"></i>
          <h3>Ecosystem Preservation</h3>
          <p>Supporting initiatives to protect coral reefs, rainforests, and coastal ecosystems.</p>
        </div>
        <div class="solution-card animate">
          <i class="fas fa-hand-holding-heart solution-icon"></i>
          <h3>Community Engagement</h3>
          <p>Partnering with local communities to promote environmental stewardship.</p>
        </div>
        <div class="solution-card animate">
          <i class="fas fa-chart-line solution-icon"></i>
          <h3>Sustainable Business Practices</h3>
          <p>Implementing eco-friendly operations and reducing our carbon footprint.</p>
        </div>
        <a href="https://saithtechnologies.com/faqs/" target="_blank" class="solution-card solution-card-small animate">
          <div class="card-content-wrapper">
            <i class="fas fa-question-circle solution-icon"></i>
            <h3>Learn More</h3>
            <p>Explore our FAQs for more details on our sustainability efforts.</p>
          </div>
        </a>
        <a href="https://m.gepecotech.com/solution/refuse-derived-fuel-system.html?utm_source=google&utm_medium=g&utm_campaign=c-searching&utm_content=732383928715&utm_term=alternative%20fuel%20production&match=b&item=&target=kwd-721567947&device=m&gad_source=1&gad_campaignid=***********&gclid=CjwKCAjwruXBBhArEiwACBRtHZ8rULOn_aFMw-uxRSDOFlTqZF1hmKpIOKDomvQ4YFiwiuolZm8AexoC8TcQAvD_BwE" 
          target="_blank" class="solution-card solution-card-small animate">
          <div class="card-content-wrapper">
            <i class="fas fa-lightbulb solution-icon"></i>
            <h3>Explore RDF Technology</h3>
            <p>Discover how Refuse Derived Fuel systems can transform waste into energy.</p>
          </div>
        </a>
      </div>
    </div>
  </section>

  <section class="section" style="background: linear-gradient(135deg, #047857 0%, #0d9488 100%); color: white; text-align: center;">
    <div class="container">
      <h2 class="section-title">Join the Green Revolution</h2>
      <p class="section-subtitle">Partner with us to build a sustainable future for the Caribbean.</p>
      <a href="about-contact.html#contact" class="btn btn-light btn-animate">Get Involved</a>
    </div>
  </section>

  <footer style="background: var(--dark); color: white; padding: 4rem 0 2rem;">
    <div class="container">
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2.5rem; margin-bottom: 3rem;">
        <div>
          <div class="logo" style="margin-bottom: 1.5rem;">
            <div class="logo-placeholder">
              <span>Q</span>
            </div>
            <span class="logo-text" style="color: white;">Quikr Solutions</span>
          </div>
          <p style="margin-bottom: 1.5rem; opacity: 0.8;">Empowering businesses and individuals through innovative technology solutions.</p>
          <div style="display: flex; gap: 1rem;">
            <a href="#" style="width: 40px; height: 40px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-decoration: none; transition: var(--transition);">
              <i class="fab fa-facebook-f"></i>
            </a>
            <a href="#" style="width: 40px; height: 40px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-decoration: none; transition: var(--transition);">
              <i class="fab fa-twitter"></i>
            </a>
            <a href="#" style="width: 40px; height: 40px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-decoration: none; transition: var(--transition);">
              <i class="fab fa-linkedin-in"></i>
            </a>
            <a href="#" style="width: 40px; height: 40px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; text-decoration: none; transition: var(--transition);">
              <i class="fab fa-instagram"></i>
            </a>
          </div>
        </div>
        
        <div>
          <h3 style="font-size: 1.3rem; margin-bottom: 1.5rem; position: relative; padding-bottom: 0.5rem;">Quick Links</h3>
          <ul style="list-style: none;">
            <li style="margin-bottom: 0.8rem;"><a href="index.html" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Home</a></li>
            <li style="margin-bottom: 0.8rem;"><a href="about.html" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">About Us</a></li>
            <li style="margin-bottom: 0.8rem;"><a href="solutions.html" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Solutions</a></li>
            <li style="margin-bottom: 0.8rem;"><a href="market.html" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Caribbean Focus</a></li>
            <li><a href="contact.html" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Contact</a></li>
          </ul>
        </div>
        
        <div>
          <h3 style="font-size: 1.3rem; margin-bottom: 1.5rem; position: relative; padding-bottom: 0.5rem;">Legal</h3>
          <ul style="list-style: none;">
            <li style="margin-bottom: 0.8rem;"><a href="#" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Privacy Policy</a></li>
            <li style="margin-bottom: 0.8rem;"><a href="#" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Terms of Service</a></li>
            <li><a href="#" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; transition: var(--transition);">Cookie Policy</a></li>
          </ul>
        </div>
        
        <div>
          <h3 style="font-size: 1.3rem; margin-bottom: 1.5rem; position: relative; padding-bottom: 0.5rem;">Contact</h3>
          <ul style="list-style: none;">
            <li style="margin-bottom: 1rem; display: flex; align-items: flex-start;">
              <i class="fas fa-envelope" style="margin-right: 1rem; color: var(--accent);"></i>
              <span><EMAIL></span>
            </li>
            <li style="margin-bottom: 1rem; display: flex; align-items: flex-start;">
              <i class="fas fa-map-marker-alt" style="margin-right: 1rem; color: var(--accent);"></i>
              <span>#3 Captain Street, Basseterre</span>
            </li>
            <li style="display: flex; align-items: flex-start;">
              <i class="fas fa-phone-alt" style="margin-right: 1rem; color: var(--accent);"></i>
              <span>+****************</span>
            </li>
          </ul>
        </div>
      </div>

      <div style="text-align: center; padding-top: 2rem; border-top: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.6); font-size: 0.9rem;">
        <p>&copy; 2023 Quikr Solutions. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <script type="module" src="js/main.js"></script>
</body>
</html>