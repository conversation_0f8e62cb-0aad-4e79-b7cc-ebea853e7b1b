header {
    background: white;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 0.5rem 0;
  }
 
  .nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
  }
 
  .logo {
    display: flex;
    align-items: center;
    text-decoration: none;
  }
 
  .logo-placeholder {
    width: 100px;
    height: 50px;
    background: linear-gradient(45deg, var(--primary), var(--primary-dark), var(--secondary));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 18px;
    animation: gradientAnimation 8s ease infinite;
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
  }
 
  .logo-placeholder span {
    color: white;
    font-size: 2rem;
    font-weight: 500;
    font-family: 'Montserrat', sans-serif;
  }
 
  .logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-dark);
  }
 
  .nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
  }
/* Dropdown Menu */
.dropdown {
position: relative;
}

.dropdown-content {
display: none;
position: absolute;
background-color: white;
min-width: 200px;
box-shadow: 0 8px 16px rgba(0,0,0,0.1);
border-radius: 10px;
padding: 0.5rem 0;
z-index: 1;
top: 100%;
left: 0;
margin-top: 0.5rem;
}

.dropdown:hover .dropdown-content {
display: block;
}

.dropdown-item {
  display: block;
  padding: 0.75rem 1.5rem;
  text-decoration: none;
  color: var(--gray);
  transition: var(--transition);
}

.dropdown-item:hover {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--primary);
}

.dropdown-header {
  padding: 0.5rem 1.5rem 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-dark);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
}

.dropdown-divider {
  height: 1px;
  background-color: var(--border);
  margin: 0.5rem 0;
}

.dropdown-trigger {
  cursor: pointer;
  color: var(--gray);
  font-weight: 500;
  transition: var(--transition);
  position: relative;
  padding: 0.5rem 0;
}

.dropdown-trigger:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 3px;
  background: var(--accent);
  transition: var(--transition);
  border-radius: 2px;
}

.dropdown-trigger:hover {
  color: var(--primary);
}

.dropdown-trigger:hover:after {
  width: 100%;
}

    .nav-link {
      text-decoration: none;
      color: var(--gray);
      font-weight: 500;
      transition: var(--transition);
      position: relative;
      padding: 0.5rem 0;
    }

  .nav-link:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--accent);
    transition: var(--transition);
    border-radius: 2px;
  }

  .nav-link:hover {
    color: var(--primary);
  }

  .nav-link:hover:after {
    width: 100%;
  }

  .nav-link.active {
    color: var(--primary);
  }

  .nav-link.active:after {
    width: 100%;
  }

  .hamburger {
    display: none;
    cursor: pointer;
    background: none;
    border: none;
    font-size: 1.8rem;
    color: var(--gray);
    z-index: 1000;
  }