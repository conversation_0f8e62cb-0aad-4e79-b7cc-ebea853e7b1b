<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delivery Services Registration - Quikr Solutions</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../components/wave.css">
</head>
<body>
    <div id="navbar-container"></div>
    <script>
      fetch('../components/navbar.html')
        .then(response => response.text())
        .then(data => {
          document.getElementById('navbar-container').innerHTML = data;
          // Initialize navigation after navbar is loaded
          if (window.initNavigation) {
            window.initNavigation();
          }
        });
    </script>

    <!-- Hero Section -->
    <section class="hero bg-gradient-accent">
        <div class="container">
            <div class="hero-content">
                <h1 class="animate">Delivery Services Registration</h1>
                <p class="animate delay-1">Join our fast-growing delivery network with bikes, scooters, and small vehicles for packages and food delivery.</p>
            </div>
        </div>
        <!-- Wave Animation -->
        <div class="wave-container">
            <svg class="wave-animation" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" class="shape-fill"></path>
            </svg>
        </div>
    </section>

    <!-- Delivery Categories Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">Delivery Categories</h2>
            <p class="section-subtitle">Choose your preferred delivery type and start earning with flexible schedules.</p>

            <div class="features-grid">
                <div class="feature-card animate">
                    <i class="fas fa-motorcycle feature-icon"></i>
                    <h3>Bike & Scooter Delivery</h3>
                    <p>Perfect for quick deliveries in urban areas with bikes and scooters.</p>
                    <ul class="feature-list">
                        <li>Food delivery</li>
                        <li>Small packages</li>
                        <li>Documents</li>
                        <li>Pharmacy items</li>
                    </ul>
                </div>

                <div class="feature-card animate">
                    <i class="fas fa-utensils feature-icon"></i>
                    <h3>Food Delivery</h3>
                    <p>Specialized food delivery services with insulated bags and quick service.</p>
                    <ul class="feature-list">
                        <li>Restaurant orders</li>
                        <li>Grocery delivery</li>
                        <li>Hot food transport</li>
                        <li>Beverage delivery</li>
                    </ul>
                </div>

                <div class="feature-card animate">
                    <i class="fas fa-box feature-icon"></i>
                    <h3>Package Delivery</h3>
                    <p>Handle small to medium packages with secure and timely delivery.</p>
                    <ul class="feature-list">
                        <li>E-commerce packages</li>
                        <li>Business documents</li>
                        <li>Personal items</li>
                        <li>Same-day delivery</li>
                    </ul>
                </div>

                <div class="feature-card animate">
                    <i class="fas fa-clock feature-icon"></i>
                    <h3>Express Delivery</h3>
                    <p>Time-sensitive deliveries with priority handling and tracking.</p>
                    <ul class="feature-list">
                        <li>1-hour delivery</li>
                        <li>Medical supplies</li>
                        <li>Emergency items</li>
                        <li>Priority packages</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Earnings & Benefits Section -->
    <section class="section bg-light">
        <div class="container">
            <h2 class="section-title">Earnings & Benefits</h2>
            <div class="two-column">
                <div>
                    <h3>Competitive Earnings</h3>
                    <div class="earnings-breakdown">
                        <div class="earning-item">
                            <span class="earning-type">Base Rate:</span>
                            <span class="earning-amount">$3-5 per delivery</span>
                        </div>
                        <div class="earning-item">
                            <span class="earning-type">Distance Bonus:</span>
                            <span class="earning-amount">$0.50 per km</span>
                        </div>
                        <div class="earning-item">
                            <span class="earning-type">Peak Hours:</span>
                            <span class="earning-amount">+25% bonus</span>
                        </div>
                        <div class="earning-item">
                            <span class="earning-type">Tips:</span>
                            <span class="earning-amount">Keep 100%</span>
                        </div>
                    </div>
                </div>
                <div>
                    <h3>Additional Benefits</h3>
                    <div class="feature-item">
                        <i class="fas fa-calendar-check"></i>
                        <div>
                            <h4>Flexible Schedule</h4>
                            <p>Work when you want, as much as you want</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-mobile-alt"></i>
                        <div>
                            <h4>Easy App</h4>
                            <p>Simple app to manage deliveries and track earnings</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-shield-alt"></i>
                        <div>
                            <h4>Insurance</h4>
                            <p>Coverage while on delivery</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-credit-card"></i>
                        <div>
                            <h4>Weekly Pay</h4>
                            <p>Get paid every week directly to your account</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Registration Form Section -->
    <section class="section">
        <div class="container">
            <div class="two-column">
                <div>
                    <h2 class="section-title">Start Delivering Today</h2>
                    <p class="section-subtitle">Join thousands of delivery partners earning flexible income.</p>
                    
                    <div class="requirements-box">
                        <h4>Requirements:</h4>
                        <ul>
                            <li>Valid driver's license or ID</li>
                            <li>Own vehicle (bike, scooter, or car)</li>
                            <li>Smartphone with GPS</li>
                            <li>Insulated delivery bag (provided)</li>
                            <li>Age 18 or older</li>
                        </ul>
                    </div>

                    <div class="process-info">
                        <h4>Getting Started:</h4>
                        <ol>
                            <li>Complete registration form</li>
                            <li>Upload required documents</li>
                            <li>Attend orientation session</li>
                            <li>Download delivery app</li>
                            <li>Start earning!</li>
                        </ol>
                    </div>
                </div>

                <div class="contact-form">
                    <h3>Delivery Partner Registration</h3>
                    <form id="delivery-registration-form">
                        <div class="form-group">
                            <label class="form-label">Full Name *</label>
                            <input type="text" class="form-control" name="full_name" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Email Address *</label>
                            <input type="email" class="form-control" name="email" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Phone Number *</label>
                            <input type="tel" class="form-control" name="phone" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Date of Birth *</label>
                            <input type="date" class="form-control" name="date_of_birth" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Address *</label>
                            <textarea class="form-control" name="address" rows="3" required></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Delivery Vehicle Type *</label>
                            <select class="form-control" name="vehicle_type" required>
                                <option value="">Select Vehicle Type</option>
                                <option value="bicycle">Bicycle</option>
                                <option value="scooter">Scooter/Motorcycle</option>
                                <option value="car">Car</option>
                                <option value="van">Small Van</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Vehicle Make & Model</label>
                            <input type="text" class="form-control" name="vehicle_model" placeholder="e.g., Honda PCX 150">
                        </div>

                        <div class="form-group">
                            <label class="form-label">Preferred Delivery Type *</label>
                            <select class="form-control" name="delivery_type" required>
                                <option value="">Select Delivery Type</option>
                                <option value="food">Food Delivery</option>
                                <option value="packages">Package Delivery</option>
                                <option value="express">Express Delivery</option>
                                <option value="all">All Types</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Preferred Working Areas</label>
                            <input type="text" class="form-control" name="working_areas" placeholder="e.g., Downtown, Uptown, Airport area">
                        </div>

                        <div class="form-group">
                            <label class="form-label">Availability *</label>
                            <select class="form-control" name="availability" required>
                                <option value="">Select Availability</option>
                                <option value="full-time">Full-time (40+ hours/week)</option>
                                <option value="part-time">Part-time (20-40 hours/week)</option>
                                <option value="weekends">Weekends only</option>
                                <option value="evenings">Evenings only</option>
                                <option value="flexible">Flexible schedule</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Previous Delivery Experience</label>
                            <select class="form-control" name="experience">
                                <option value="">Select Experience</option>
                                <option value="none">No experience</option>
                                <option value="less-1">Less than 1 year</option>
                                <option value="1-3">1-3 years</option>
                                <option value="3+">3+ years</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Emergency Contact Name</label>
                            <input type="text" class="form-control" name="emergency_contact_name">
                        </div>

                        <div class="form-group">
                            <label class="form-label">Emergency Contact Phone</label>
                            <input type="tel" class="form-control" name="emergency_contact_phone">
                        </div>

                        <div class="form-group">
                            <label class="form-label">Additional Information</label>
                            <textarea class="form-control" name="additional_info" rows="3" placeholder="Tell us about your availability, special skills, or any questions..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-container">
                                <input type="checkbox" name="terms_accepted" required>
                                <span class="checkmark"></span>
                                I agree to the <a href="#" target="_blank">Terms and Conditions</a> and <a href="#" target="_blank">Privacy Policy</a>
                            </label>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-container">
                                <input type="checkbox" name="background_check_consent" required>
                                <span class="checkmark"></span>
                                I consent to a background check as required by law
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">Submit Application</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>Quikr Solutions</h3>
                    <p>Building sustainable solutions for the Caribbean and beyond.</p>
                </div>
                <div class="footer-column">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="../about-contact.html">About</a></li>
                        <li><a href="index.html">Solutions</a></li>
                        <li><a href="../about-contact.html#contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Connect With Us</h3>
                    <div class="social-icons">
                        <a href="#" aria-label="Facebook">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C6.477 2 2 6.477 2 12C2 17.523 6.477 22 12 22C17.523 22 22 17.523 22 12C22 6.477 17.523 2 12 2Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M15 8H13C12.4696 8 11.9609 8.21071 11.5858 8.58579C11.2107 8.96086 11 9.46957 11 10V22" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 13H16" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="Twitter">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 4.01C21 4.5 20.02 4.69 19 5C17.879 3.735 16.217 3.665 14.62 4.263C13.023 4.861 11.977 6.323 12 8.01V9.01C8.755 9.083 5.865 7.605 4 5.01C4 5.01 -0.182 12.433 8 16.01C6.128 17.247 4.261 18.088 2 18.01C5.308 19.687 8.913 20.322 12.034 19.503C15.614 18.565 18.556 15.935 19.685 11.882C20.0218 10.4988 20.1789 9.07701 20.152 7.653C20.152 7.493 21.692 5.513 22 4.009V4.01Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="Instagram">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <rect x="2" y="2" width="20" height="20" rx="5" ry="5" stroke="white" stroke-width="1.5"/>
                                <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37Z" stroke="white" stroke-width="1.5"/>
                                <line x1="17.5" y1="6.5" x2="17.51" y2="6.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Quikr Solutions. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script type="module" src="../js/main.js"></script>
</body>
</html>
