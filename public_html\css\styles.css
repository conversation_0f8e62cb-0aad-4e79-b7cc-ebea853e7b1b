/* Typography */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@600;700;800&family=Open+Sans:wght@400;600&display=swap');

/* styles.css - Main stylesheet that imports all other CSS files */

/* Import other CSS files */
@import url('animations.css');
@import url('layout.css');
@import url('responsive.css');
@import url('../components/navbar.css');

/* Global styles */
:root {
  /* Color variables */
  --primary: #4361ee;
  --primary-dark: #3a0ca3;
  --secondary: #7209b7;
  --accent: #f72585;
  --green: #10b981;
  --green-dark: #059669;
  --light-blue: #4cc9f0;
  --light: #f8f9fa;
  --dark: #212529;
  --gray: #6c757d;
  --border: #dee2e6;
  --card-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  
  /* Transition variables */
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Open Sans', sans-serif;
  line-height: 1.7;
  color: #333;
  background-color: #fff;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  line-height: 1.3;
}

a {
  text-decoration: none;
  color: var(--primary);
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
  transition: var(--transition);
}

img {
  max-width: 100%;
  height: auto;
}

/* Common elements */
.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.section {
  padding: 6rem 0;
  position: relative;
}

.section-title {
  font-size: 2.8rem;
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
  color: var(--primary-dark);
}

.section-title:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 70px;
  height: 4px;
  background: var(--accent);
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.25rem;
  color: var(--gray);
  max-width: 700px;
  margin-bottom: 3rem;
  line-height: 1.8;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-decoration: none;
  transition: var(--transition);
  cursor: pointer;
  border: none;
  background-color: var(--primary);
  color: white;
}

.btn:hover {
  background-color: var(--primary-dark);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--primary);
  color: var(--primary);
}

.btn-outline:hover {
  background: var(--primary);
  color: white;
}

.btn-accent {
  background-color: var(--accent);
}

.btn-accent:hover {
  background-color: #e60e6d;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-1 {
  margin-bottom: 0.5rem;
}

.mb-2 {
  margin-bottom: 1rem;
}

.mb-3 {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 2rem;
}

.mb-5 {
  margin-bottom: 3rem;
}

.mt-1 {
  margin-top: 0.5rem;
}

.mt-2 {
  margin-top: 1rem;
}

.mt-3 {
  margin-top: 1.5rem;
}

.mt-4 {
  margin-top: 2rem;
}

.mt-5 {
  margin-top: 3rem;
}

.p-1 {
  padding: 0.5rem;
}

.p-2 {
  padding: 1rem;
}

.p-3 {
  padding: 1.5rem;
}

.p-4 {
  padding: 2rem;
}

.p-5 {
  padding: 3rem;
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

/* Background colors */
.bg-primary {
  background-color: var(--primary);
  color: white;
}

.bg-secondary {
  background-color: var(--secondary);
  color: white;
}

.bg-accent {
  background-color: var(--accent);
  color: white;
}

.bg-light {
  background-color: var(--light);
}

.bg-gray {
  background-color: var(--gray);
  color: white;
}

/* Text colors */
.text-primary {
  color: var(--primary);
}

.text-secondary {
  color: var(--secondary);
}

.text-accent {
  color: var(--accent);
}

.text-light {
  color: var(--light);
}

.text-gray {
  color: var(--gray);
}

/* Gradient backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  color: white;
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, #7209b7 0%, #3a0ca3 100%);
  color: white;
}

.bg-gradient-accent {
  background: linear-gradient(135deg, #f72585 0%, #7209b7 100%);
  color: white;
}

/* Story Section */
.story-section {
  background-color: var(--light);
}

.story-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 3rem;
  border-radius: 16px;
  background-color: white;
  box-shadow: var(--card-shadow);
}

@media (min-width: 768px) {
  .story-container {
    flex-direction: row;
  }
}

.story-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.story-content {
  flex: 2;
}

.milestones {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 2rem;
}

.milestone-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  border-radius: 12px;
  background-color: var(--light-blue);
  color: white;
  text-align: center;
  width: 150px;
}

.milestone-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

/* Team Section */
.team-section {
  background-color: var(--light);
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.team-card {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
  padding: 2rem;
}

.team-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--gray);
  color: white;
  font-size: 3rem;
}

.team-image i {
  font-size: 3rem;
}

.team-info {
  flex: 1;
}

.team-info h4 {
  margin-bottom: 0.5rem;
  color: var(--primary-dark);
}

.team-info .position {
  font-style: italic;
  color: var(--gray);
  margin-bottom: 1rem;
}

.team-social {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.social-link {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary);
  color: white;
  transition: var(--transition);
}

.social-link:hover {
  background-color: var(--primary-dark);
}

.contact-form {
  padding: 2rem;
  border-radius: 16px;
  background-color: white;
  box-shadow: var(--card-shadow);
}

.contact-form h3 {
  margin-bottom: 1.5rem;
  color: var(--primary-dark);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  font-weight: 600;
  color: var(--gray);
  margin-bottom: 0.5rem;
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border);
  border-radius: 8px;
  font-size: 1rem;
  font-family: 'Open Sans', sans-serif;
  transition: var(--transition);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 5px rgba(67, 97, 238, 0.2);
}
.values-section {
  background-color: var(--light);
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}
.value-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem;
  border-radius: 16px;
  background-color: white;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
}
.value-icon {
  font-size: 3rem;
  color: var(--primary);
  margin-bottom: 1.5rem;
}
.value-card h3 {
  margin-bottom: 1rem;
  color: var(--primary-dark);
}

/* ===== DONATION STYLES ===== */
.donation-hero {
  background: linear-gradient(135deg, #0d9488 0%, #047857 100%);
  color: white;
  padding: 8rem 0 6rem;
  position: relative;
  overflow: hidden;
}

.donation-hero:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHBhdGggZD0iTTAgMGg0MHY0MEgweiIgZmlsbD0ibm9uZSIvPjxwYXRoIGQ9Ik0xMSAwdjQwTTAgMTFoNDBNMCAwaDQwdjQwSDB6IiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMC41IiBvcGFjaXR5PSIwLjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjcGF0dGVybikiLz48L3N2Zz4=');
  opacity: 0.15;
}

.donation-hero .hero-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
}

.donation-hero .hero-content h1 {
  font-size: 3.8rem;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  animation: fadeInUp 1s ease;
}

.donation-hero .hero-content p {
  font-size: 1.3rem;
  margin-bottom: 2.5rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  animation: fadeInUp 1s ease 0.2s forwards;
  opacity: 0;
}

/* ===== DONATION OPTIONS SECTION ===== */
.donation-options {
  padding: 5rem 0;
}

.donation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}
