/* layout.css - Contains all layout-related styles using Flexbox and Grid */

/* Header and Navigation */
/* Header styling moved to navbar.css */



.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  color: white;
  position: relative;
  overflow: hidden;
  padding: 7rem 0;
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

/* Cards */
.card {
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
  border: none;
  height: 100%;
  padding: 2rem;
}

.card-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Two-column layout */
.two-column {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .two-column {
    grid-template-columns: 1fr 1fr;
  }
}

/* Three-column layout */
.three-column {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .three-column {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 992px) {
  .three-column {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

/* Footer */
footer {
  background: var(--secondary);
  color: white;
  padding: 4rem 0 2rem;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.footer-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-column ul li {
  margin-bottom: 0.75rem;
}

.social-icons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.copyright {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Forms */
.form-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .form-container {
    grid-template-columns: 1fr 1fr;
  }
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

/* Pricing Table */
.pricing-table {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

/* Centered content */
.centered-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* Button container */
.button-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

/* Image grid */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

/* Flex utilities */
.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-content-center {
  justify-content: center;
}

.align-items-center {
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.gap-1 {
  gap: 0.5rem;
}

.gap-2 {
  gap: 1rem;
}

.gap-3 {
  gap: 1.5rem;
}

.gap-4 {
  gap: 2rem;
}

/* Feature Cards */
.feature-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 3rem;
  color: var(--primary);
  margin-bottom: 1.5rem;
}

.feature-card h3 {
  margin-bottom: 1rem;
  color: var(--primary-dark);
}

.feature-card p {
  flex: 1;
  margin-bottom: 1rem;
}

/* Hover effects */
.hover-lift {
  transition: var(--transition);
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

/* Table responsive */
.table-responsive {
  overflow-x: auto;
  margin: 2rem 0;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
}

.comparison-table th,
.comparison-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border);
}

.comparison-table th {
  background-color: var(--light);
  font-weight: 600;
  color: var(--primary-dark);
}

.comparison-table tr:last-child td {
  border-bottom: none;
}

/* Footer bottom */
.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 2rem;
}
